import React, { useEffect } from "react";
import { Route } from "react-router-dom";
import { useScale } from "hooks";
import pageKiosk from "router/pageKiosk";
import { Main } from "./styled";

const KiosPages = (props) => {
  const { onScale, onUnscale } = useScale();
  useEffect(() => {
    onUnscale();
    return () => {
      onScale();
    };
  }, []);

  return (
    <Main className={"app-contain"}>
      {Object.keys(pageKiosk).map((key) => (
        <Route
          key={key}
          path={pageKiosk[key].path}
          component={pageKiosk[key].component}
          exact={pageKiosk[key].exact}
        />
      ))}
    </Main>
  );
};

export default KiosPages;
