import styled, { keyframes } from "styled-components";
import {
  titleColor,
  textColor,
  subtitleColor,
  customGray,
  customBlue2,
  btnGreen,
} from "../../common/variables";

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const slideInFromBottom = keyframes`
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const breakpoints = {
  mobile: "768px",
  tablet: "1024px",
  desktop: "1440px",
  kiosk: "1920px",
  kioskPortrait: "1080px",
  kioskLandscape: "1920px",
};

const kioskFontSizes = {
  h1: "clamp(24px, 2.5vw, 32px)",
  h2: "clamp(20px, 2vw, 28px)",
  body: "clamp(16px, 1.5vw, 20px)",
  small: "clamp(12px, 1vw, 16px)",
};

export const MainWrapper = styled.div`
  overflow: auto;
  text-align: center;
  height: 100%;
  animation: ${fadeIn} 0.5s ease-out;

  @media (orientation: portrait) {
    padding: clamp(5px, 1vh, 10px);
  }

  @media (orientation: landscape) {
    padding: clamp(8px, 1.5vh, 15px);
  }

  .form-custom {
    display: flex;
    flex-direction: column;
    height: 100%;

    @media (orientation: landscape) and (min-width: 1024px) {
      padding: clamp(15px, 2vw, 25px);
      gap: clamp(10px, 1.5vw, 20px);
    }

    .ant-row {
      @media (orientation: landscape) and (min-width: 1024px) {
        margin-bottom: clamp(8px, 1vw, 12px);

        /* Tối ưu layout cho landscape - 2 cột cho các field nhỏ */
        &:has(.ant-col[span="14"]) {
          .ant-col[span="14"] {
            flex: 0 0 58.333333%;
            max-width: 58.333333%;
          }
          .ant-col[span="10"] {
            flex: 0 0 41.666667%;
            max-width: 41.666667%;
          }
        }
      }
    }

    .ant-form-item {
      margin-bottom: 0;

      @media (orientation: landscape) and (min-width: 1024px) {
        margin-bottom: clamp(8px, 1vw, 12px);
      }
.ant-form-item-label > label { 
  width: 100%;
}
      .ant-form-item-label {
        padding-bottom: clamp(8px, 1.2vw, 12px) !important;
        & > label [
          width: 100%;
        ]

        @media (orientation: landscape) and (min-width: 1024px) {
          padding-bottom: clamp(6px, 0.8vw, 10px) !important;
        }

        label {
          font-size: clamp(16px, 2.5vw, 22px) !important;
          font-weight: 700;
          color: #2c3e50;
          line-height: 1.4;
          text-transform: uppercase;
          letter-spacing: 0.3px;

          @media (orientation: landscape) and (min-width: 1024px) {
            font-size: clamp(10px, 1.1vw, 14px) !important;
            font-weight: 500;
            letter-spacing: 0px;
            margin-bottom: clamp(4px, 0.6vw, 8px) !important;
          }

          &::after {
            display: none; // Ẩn dấu : mặc định
          }
        }
      }

      .ant-form-item-explain-error {
        font-size: clamp(10px, 1.8vw, 14px);
        margin-left: clamp(5px, 1vw, 10px);
        text-align: left;
      }

      .ant-input-affix-wrapper {
        border-radius: clamp(25px, 4vw, 32px);
        box-shadow: 0 0 0 3px #0062ff47 !important;
        overflow: hidden;
      }
      .address-full-wrapper {
        border-radius: clamp(25px, 4vw, 32px);
        box-shadow: 0 0 0 3px #0062ff47 !important;
        padding: 4px 11px;
        position: relative;

        @media (orientation: landscape) and (min-width: 1024px) {
          border-radius: clamp(16px, 2vw, 20px);
          padding: 2px 8px;
        }
        ul {
          position: absolute;
          width: 100%;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15),
            0 2px 8px rgba(0, 0, 0, 0.08);
          z-index: 9999;
          max-height: 280px;
          overflow-y: auto;
          border: 2px solid #e8f0fe;
          margin-top: 8px;
          left: 0;

          @media (orientation: landscape) and (min-width: 1024px) {
            max-height: clamp(80px, 12vh, 120px);
            z-index: 9999;
          }
          padding: 8px 0;
          list-style: none;

          ::-webkit-scrollbar {
            width: 8px;
          }
          ::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 4px;
          }
          ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #0762f7, #4285f4);
            border-radius: 4px;
            border: 1px solid #e8f0fe;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0651d3, #3367d6);
          }

          li {
            font-size: clamp(16px, 2.5vw, 22px);
            text-align: left;
            padding: 12px 16px;
            margin: 2px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #2c3e50;
            font-weight: 500;
            line-height: 1.4;
            border: 1px solid transparent;

            @media (orientation: landscape) and (min-width: 1024px) {
              font-size: clamp(10px, 1.1vw, 13px);
              padding: clamp(4px, 0.8vw, 6px) clamp(8px, 1.2vw, 12px);
              margin: 1px 4px;
              line-height: 1.2;
            }

            &:hover {
              background: linear-gradient(135deg, #f0f6ff 0%, #e8f4fd 100%);
              border-color: #0762f7;
              color: #0762f7;
              transform: translateX(4px);
              box-shadow: 0 2px 8px rgba(7, 98, 247, 0.15);
            }

            &:active {
              transform: translateX(2px);
              background: linear-gradient(135deg, #e8f4fd 0%, #dbeafe 100%);
            }
          }

          li:last-child {
            margin-bottom: 0;
          }
        }
      }

      .ant-input,
      .ant-picker,
      .address-full-wrapper input,
      .item-dob input {
        height: clamp(50px, 7vw, 65px) !important;
        font-size: ${kioskFontSizes.body};
        padding: clamp(12px, 2vw, 18px) clamp(18px, 3vw, 24px) !important;
        transition: all 0.3s ease;
        background: #ffffff;
        border: none !important;
        box-shadow: none !important;
        font-weight: 500;

        &::placeholder {
          color: #8b9bb8;
          opacity: 0.9;
          font-weight: 400;
        }
      }

      /* Responsive riêng cho landscape */
      @media (orientation: landscape) and (min-width: 1024px) {
        .ant-input,
        .ant-picker,
        .address-full-wrapper input,
        .item-dob input {
          height: clamp(32px, 3.5vw, 40px) !important;
          font-size: clamp(11px, 1.2vw, 14px);
          padding: clamp(6px, 1vw, 10px) clamp(10px, 1.2vw, 14px) !important;
        }
      }

      /* iPad Pro và màn hình lớn (portrait) */
      @media (orientation: portrait) and (min-width: 1024px) and (min-height: 1200px) {
        .ant-input,
        .ant-picker,
        .address-full-wrapper input,
        .item-dob input {
          height: clamp(45px, 5vw, 55px) !important;
          font-size: clamp(16px, 2vw, 20px);
          padding: clamp(10px, 1.5vw, 15px) clamp(15px, 2vw, 20px) !important;
        }
      }

      /* Màn hình dọc nhỏ (dưới 1080px) */
      @media (orientation: portrait) and (max-width: 1079px) {
        .ant-input,
        .ant-picker,
        .address-full-wrapper input,
        .item-dob input {
          height: clamp(40px, 6vw, 50px) !important;
          font-size: clamp(14px, 2.2vw, 18px);
          padding: clamp(8px, 1.2vw, 12px) clamp(12px, 2vw, 16px) !important;
        }
      }
      .address-full-wrapper input {
        width: 100%;
      }
      .ant-input-suffix {
        span {
          font-size: ${kioskFontSizes.body};
        }
      }

      .ant-picker {
        width: 100%;

        .ant-picker-input input {
          font-size: ${kioskFontSizes.body};
          font-weight: 500;
        }

        .ant-picker-suffix {
          font-size: ${kioskFontSizes.body};
          color: #0762f7;
        }
      }

      /* Đảm bảo DOBInput có kích thước phù hợp */
      .item-dob {
        .ant-picker {
          height: clamp(50px, 7vw, 65px) !important;
          border-radius: clamp(25px, 4vw, 32px) !important;
          border: 3px solid #0062ff47 !important;

          .ant-picker-input input {
            font-size: ${kioskFontSizes.body} !important;
            padding: clamp(12px, 2vw, 18px) clamp(18px, 3vw, 24px) !important;
          }

          .ant-picker-suffix {
            font-size: ${kioskFontSizes.body} !important;
            margin-right: clamp(12px, 2vw, 18px);
          }
        }

        .ant-picker-dropdown {
          .ant-picker-panel-container {
            font-size: ${kioskFontSizes.body};
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          }

          .ant-picker-header {
            button {
              font-size: ${kioskFontSizes.body};
            }
          }

          .ant-picker-content {
            th,
            td {
              font-size: ${kioskFontSizes.small};
              height: 32px;
              line-height: 32px;
            }
          }
        }
      }

      .ant-radio-group {
        display: flex;
        gap: clamp(25px, 4vw, 35px);
        margin-top: clamp(8px, 1.5vw, 12px);

        @media (orientation: landscape) and (min-width: 1024px) {
          gap: clamp(15px, 2.5vw, 20px);
          margin-top: clamp(6px, 1vw, 10px);
        }

        .ant-radio-wrapper {
          font-size: clamp(16px, 2.5vw, 22px);
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          cursor: pointer;

          @media (orientation: landscape) and (min-width: 1024px) {
            font-size: clamp(10px, 1.1vw, 14px);
            font-weight: 500;
          }

          .ant-radio {
            .ant-radio-inner {
              width: clamp(20px, 3vw, 26px);
              height: clamp(20px, 3vw, 26px);
              border-color: #0762f7;

              @media (orientation: landscape) and (min-width: 1024px) {
                width: clamp(14px, 1.6vw, 18px);
                height: clamp(14px, 1.6vw, 18px);
              }

              &::after {
                width: clamp(20px, 3vw, 26px);
                height: clamp(20px, 3vw, 26px);
                background-color: #0762f7;
                margin-top: 0;
                margin-left: 0;
                transform: translateX(-50%) translateY(-50%) scale(0.5);

                @media (orientation: landscape) and (min-width: 1024px) {
                  width: clamp(14px, 1.6vw, 18px);
                  height: clamp(14px, 1.6vw, 18px);
                }
              }
            }
          }
        }

        .ant-checkbox-wrapper {
          font-size: clamp(16px, 2.5vw, 22px);
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          cursor: pointer;

          @media (orientation: landscape) and (min-width: 1024px) {
            font-size: clamp(10px, 1.1vw, 14px);
            font-weight: 500;
          }

          .ant-checkbox {
            .ant-checkbox-inner {
              width: clamp(20px, 3vw, 26px);
              height: clamp(20px, 3vw, 26px);
              border-color: #0762f7;

              @media (orientation: landscape) and (min-width: 1024px) {
                width: clamp(14px, 1.6vw, 18px);
                height: clamp(14px, 1.6vw, 18px);
              }

              &::after {
                width: clamp(6px, 1vw, 8px);
                height: clamp(10px, 1.5vw, 14px);
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(0);
                transition: all 0.2s ease-in-out;

                @media (orientation: landscape) and (min-width: 1024px) {
                  width: clamp(4px, 0.6vw, 6px);
                  height: clamp(8px, 1vw, 10px);
                }
              }
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
              background-color: #0762f7;
              border-color: #0762f7;

              &::after {
                transform: rotate(45deg) scale(1);
              }
            }
          }
        }
      }
    }

    .helper-text {
      font-size: ${kioskFontSizes.small};
      color: #7a869a;
      font-style: italic;
      line-height: 1.3;
      text-align: left;
      margin-left: clamp(10px, 2vw, 15px);
      margin-top: clamp(5px, 1vw, 10px);

      @media (orientation: landscape) and (min-width: 1024px) {
        font-size: clamp(9px, 1vw, 12px);
        margin-left: clamp(4px, 0.8vw, 8px);
        margin-top: clamp(2px, 0.4vw, 4px);
      }

      &.error-msg {
        color: #ff4d4f;
      }
    }

    .ant-form-item-has-error + .helper-text,
    .ant-form-item-has-error .helper-text {
      display: none;
    }

    .btn-save {
      font-size: ${kioskFontSizes.body};
      font-weight: 700;

      svg {
        width: clamp(20px, 2.5vw, 28px);
        height: clamp(20px, 2.5vw, 28px);
        color: #ffffff;
      }

      span {
        font-size: ${kioskFontSizes.body};
        font-weight: 700;
      }
    }
  }

  .top {
    overflow: hidden;
    padding-bottom: clamp(14px, 2vw, 20px);

    .header {
      display: flex;
      flex-direction: column;
      padding: clamp(10px, 2vw, 15px);
      gap: clamp(10px, 2vw, 15px);

      .title {
        font-weight: 800;
        font-size: ${kioskFontSizes.h1};
        line-height: 1.2;
        text-align: center;
        letter-spacing: 0.01em;
        text-transform: uppercase;
        color: ${titleColor};
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 0;
      }

      .sub-header {
        color: ${subtitleColor};
        font-weight: 400;
        font-size: ${kioskFontSizes.body};
        margin: 0;
        opacity: 0.9;
      }
    }

    .desc {
      margin: clamp(20px, 4vw, 40px) clamp(15px, 3vw, 30px) 0;
      font-weight: bold;
      font-size: ${kioskFontSizes.h2};
      line-height: 1.3;
      text-align: center;
      letter-spacing: 0.01em;
      color: ${textColor};
    }
  }

  .content {
    width: clamp(350px, 90vw, 900px);
    max-width: 95vw;
    flex: 1;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    margin-bottom: clamp(8px, 1.5vw, 15px);
    border-top-left-radius: clamp(12px, 2.5vw, 24px);
    border-top-right-radius: clamp(12px, 2.5vw, 24px);
    background: linear-gradient(135deg, #f0f6ff 0%, #e8f4fd 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: ${slideInFromBottom} 0.6s ease-out;

    &.no-header {
      margin-top: clamp(20px, 4vw, 50px);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: clamp(8px, 1.5vw, 16px);
      padding: clamp(12px, 2vw, 20px) 0;
      font-size: ${kioskFontSizes.h2};
      font-weight: 700;
      color: ${titleColor};

      svg {
        width: clamp(24px, 3vw, 40px) !important;
        height: clamp(24px, 3vw, 40px) !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }
    }

    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: clamp(10px, 1.8vw, 20px);

      @media (orientation: landscape) and (min-width: 1024px) {
        padding: clamp(15px, 2.5vw, 30px) clamp(20px, 3vw, 40px);
      }

      h1 {
        font-size: ${kioskFontSizes.h1};
        font-weight: 700;
        margin-bottom: clamp(15px, 2vw, 25px);
        color: ${titleColor};
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        @media (orientation: landscape) and (min-width: 1024px) {
          font-size: clamp(20px, 2.2vw, 28px);
          margin-bottom: clamp(12px, 1.5vw, 18px);
        }
      }

      p {
        font-size: ${kioskFontSizes.body};
        color: ${subtitleColor};
        line-height: 1.5;
        margin-bottom: clamp(25px, 4vw, 40px);
        opacity: 0.9;

        @media (orientation: landscape) and (min-width: 1024px) {
          font-size: clamp(14px, 1.8vw, 18px);
          margin-bottom: clamp(15px, 2.5vw, 25px);
          line-height: 1.4;
        }
      }

      .icon-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: clamp(10px, 1.8vw, 20px);
        margin-bottom: clamp(15px, 2vw, 25px);

        @media (orientation: landscape) and (min-width: 1024px) {
          gap: clamp(15px, 2.5vw, 30px);
          margin-bottom: clamp(20px, 3vw, 35px);
        }

        svg {
          height: clamp(80px, 12vh, 160px);
          width: auto;
          max-width: 25%;
          object-fit: contain;

          @media (orientation: landscape) and (min-width: 1024px) {
            height: clamp(60px, 8vh, 100px);
            max-width: 30%;
          }
        }
      }

      .input-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: clamp(15px, 2vw, 25px);
        margin-top: clamp(10px, 2vw, 20px);

        .input-search-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: clamp(15px, 2vw, 25px);
          width: 100%;
          max-width: 600px;

          @media (orientation: landscape) and (min-width: 1024px) {
            flex-direction: row;
            gap: clamp(20px, 3vw, 30px);
            max-width: 900px;
          }
        }

        .input-search {
          background: #ffffff;
          border: 3px solid ${btnGreen};
          box-sizing: border-box;
          border-radius: clamp(25px, 4vw, 50px);
          display: flex;
          min-width: 300px;
          width: 100%;
          max-width: 600px;
          align-items: center;
          justify-content: center;
          padding: clamp(12px, 2vw, 20px) clamp(16px, 2.5vw, 24px);
          box-shadow: 0 4px 16px rgba(4, 146, 84, 0.2);
          transition: all 0.3s ease;
          position: relative;

          @media (orientation: landscape) and (min-width: 1024px) {
            flex: 1;
            max-width: none;
          }

          &:hover {
            box-shadow: 0 6px 24px rgba(4, 146, 84, 0.3);
            transform: translateY(-2px);
          }

          &:focus-within {
            box-shadow: 0 0 0 4px rgba(4, 146, 84, 0.2);
            border-color: ${btnGreen};
          }

          .ant-input-affix-wrapper {
            border: none;
            background: transparent;
            font-size: ${kioskFontSizes.body};

            &:focus,
            &-focused {
              border: none;
              box-shadow: none;
            }
          }

          input {
            border: none;
            font-weight: 600;
            font-size: ${kioskFontSizes.body} !important;
            line-height: 1.4;
            color: ${textColor};

            &:hover,
            &:focus {
              border: none !important;
              box-shadow: none !important;
            }

            &::placeholder {
              color: #7a869a;
              opacity: 0.7;
            }

            &:disabled {
              background: transparent;
              color: ${textColor};
              opacity: 0.7;
            }
          }

          .ant-input-suffix {
            svg {
              width: clamp(20px, 2.5vw, 32px);
              height: clamp(20px, 2.5vw, 32px);
              color: ${btnGreen};
            }
          }
        }

        .btn-action-inline {
          display: none;

          @media (orientation: landscape) and (min-width: 1024px) {
            display: block;
          }

          .btn-dang-ky {
            background: #ffffff;
            border: none;
            border-radius: 32px;
            color: ${btnGreen};
            font-weight: 700;
            font-size: clamp(16px, 2vw, 20px);
            padding: 0 clamp(20px, 3vw, 30px);
            height: clamp(60px, 8vh, 80px);
            display: flex;
            align-items: center;
            gap: clamp(8px, 1.5vw, 12px);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0px 8px 0px ${btnGreen},
              0px 4px 10px rgb(10 47 108 / 30%);
            min-width: clamp(140px, 20vw, 200px);
            justify-content: center;

            &:hover:not(:disabled) {
              transform: translateY(-2px);
            }

            &:active:not(:disabled) {
              transform: translateY(2px);
              box-shadow: 0px 4px 0px ${btnGreen},
                0px 2px 5px rgb(10 47 108 / 30%);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              transform: none;
            }

            svg {
              width: clamp(18px, 2.5vw, 24px);
              height: clamp(18px, 2.5vw, 24px);

              path {
                fill: ${btnGreen};
              }
            }

            span {
              font-size: clamp(16px, 2vw, 20px);
              font-weight: 700;
            }
          }
        }

        .loading-text {
          font-size: ${kioskFontSizes.body};
          color: ${btnGreen};
          font-weight: 600;
          animation: ${pulse} 1.5s ease-in-out infinite;
          text-align: center;
        }
      }

      .btn-action {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        padding-bottom: clamp(10px, 1.8vw, 20px);
        font-size: ${kioskFontSizes.body};
        .btn-group {
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 100%;
          gap: clamp(15px, 2vw, 25px);

          @media (orientation: landscape) and (min-width: 1024px) {
            justify-content: center;
            gap: clamp(20px, 3vw, 30px);
            flex-wrap: wrap;
          }
        }

        &.btn-action-bottom {
          @media (orientation: landscape) and (min-width: 1024px) {
            display: none;
          }
        }
      }

      .success-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: clamp(5px, 1vw, 12px);
        width: 100%;
        height: 100%;
        animation: ${fadeIn} 0.8s ease-out;

        svg {
          width: clamp(40px, 5vw, 70px) !important;
          height: clamp(40px, 5vw, 70px) !important;
          filter: drop-shadow(0 4px 8px rgba(4, 146, 84, 0.3));
          animation: ${pulse} 2s ease-in-out infinite;
        }

        h1 {
          font-size: ${kioskFontSizes.h2};
          font-weight: 700;
          color: ${btnGreen};
          text-align: center;
          line-height: 1.2;
          text-shadow: 0 2px 4px rgba(4, 146, 84, 0.2);
          margin: 0 clamp(15px, 2vw, 25px);
        }
        p {
          font-size: ${kioskFontSizes.h2};
          color: ${subtitleColor};
        }

        .stt-content {
          margin: clamp(8px, 1.2vw, 15px) auto;
          background: linear-gradient(135deg, ${customGray} 0%, #f5f8fa 100%);
          border: clamp(2px, 0.2vw, 3px) dashed ${customBlue2};
          box-sizing: border-box;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
          border-radius: clamp(16px, 2.5vw, 32px);
          width: clamp(280px, 45vw, 420px);
          padding: clamp(15px, 2vw, 25px) clamp(12px, 1.5vw, 20px);
          text-align: center;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, ${customBlue2}, ${btnGreen});
            border-radius: inherit;
            z-index: -1;
            opacity: 0.08;
          }

          .stt {
            font-size: ${kioskFontSizes.body};
            line-height: 1.3;
            color: ${textColor};
            margin-bottom: clamp(8px, 1vw, 12px);
            font-weight: 600;
          }

          .number {
            font-size: clamp(36px, 8vw, 80px);
            line-height: 0.9;
            color: ${customBlue2};
            font-weight: 900;
            text-shadow: -1px -1px 2px rgba(255, 255, 255, 0.8),
              1px 1px 4px rgba(0, 0, 0, 0.15);
            margin: clamp(5px, 1vw, 10px) 0;
            animation: ${pulse} 3s ease-in-out infinite;
          }

          .sub-txt {
            color: #e53935;
            font-size: ${kioskFontSizes.body};
          }

          .guideline {
            color: ${textColor};
            padding: clamp(8px, 1.2vw, 15px);
            font-size: ${kioskFontSizes.small};
            line-height: 1.3;
            opacity: 0.8;
          }
        }

        .footer {
          margin-top: clamp(8px, 1vw, 15px);

          .footer-text {
            text-align: center;
            color: ${titleColor};
            font-size: ${kioskFontSizes.small};
            font-weight: 600;
            margin-bottom: clamp(5px, 0.8vw, 10px);
          }

          .image {
            text-align: center;

            img {
              height: clamp(24px, 2.5vw, 35px);
              width: clamp(24px, 2.5vw, 35px);
              filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
              animation: ${pulse} 2s ease-in-out infinite;
            }
          }
        }
      }

      .error-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: clamp(15px, 2.5vw, 30px);
        width: 100%;
        height: 100%;
        animation: ${fadeIn} 0.8s ease-out;
        padding: clamp(20px, 3vw, 40px);

        svg {
          width: clamp(60px, 8vw, 120px) !important;
          height: clamp(60px, 8vw, 120px) !important;
          filter: drop-shadow(0 4px 8px rgba(229, 57, 53, 0.3));
        }

        h1 {
          font-size: ${kioskFontSizes.h2};
          color: #e53935;
          text-align: center;
          line-height: 1.4;
          font-weight: 600;
          margin: 0 clamp(20px, 3vw, 40px);
        }
        span {
          color: ${textColor};
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .content {
      width: 98vw;
      margin-bottom: 5px;
      border-radius: 8px;

      &.no-header {
        margin-top: clamp(10px, 2vw, 25px);
      }

      .card-content {
        padding: 8px 12px;

        h1 {
          font-size: clamp(18px, 4vw, 28px);
          margin-bottom: 8px;
        }

        p {
          font-size: clamp(12px, 2.5vw, 16px);
          margin-bottom: 8px;
        }
      }
    }

    /* Form responsive styles for tablets */
    .form-dang-ky-kham {
      padding: clamp(15px, 2.5vw, 25px);
      max-width: 95vw;

      .form-header {
        margin-bottom: clamp(15px, 2vw, 20px);
        padding-bottom: clamp(10px, 1.5vw, 15px);

        span {
          font-size: clamp(16px, 3vw, 24px);
        }
      }

      .form-custom {
        .ant-form-item {
          .ant-input,
          .ant-picker {
            height: clamp(40px, 5vw, 50px);
            font-size: clamp(14px, 2.5vw, 18px);
            padding: clamp(6px, 1.2vw, 12px) clamp(12px, 2vw, 16px);
            .ant-picker-suffix {
              font-size: clamp(14px, 2.5vw, 18px);
            }
          }

          /* Responsive styles cho DOBInput */
          .item-dob {
            .ant-picker {
              height: clamp(40px, 5vw, 50px) !important;
              border-radius: clamp(20px, 3vw, 25px) !important;

              .ant-picker-input input {
                font-size: clamp(14px, 2.5vw, 18px) !important;
                padding: clamp(6px, 1.2vw, 12px) clamp(12px, 2vw, 16px) !important;
              }

              .ant-picker-suffix {
                font-size: clamp(14px, 2.5vw, 18px) !important;
                margin-right: clamp(8px, 1.5vw, 12px);
              }
            }
          }

          .ant-form-item-explain-error {
            font-size: clamp(10px, 1.8vw, 14px);
            margin-left: clamp(5px, 1vw, 10px);
            text-align: left;
          }

          .ant-radio-group {
            gap: clamp(20px, 3vw, 25px);

            .ant-radio-wrapper {
              font-size: clamp(14px, 2.5vw, 18px);

              .ant-radio .ant-radio-inner {
                width: clamp(16px, 2vw, 20px);
                height: clamp(16px, 2vw, 20px);

                &::after {
                  width: clamp(16px, 2vw, 20px);
                  height: clamp(16px, 2vw, 20px);
                }
              }
            }
          }

          .ant-checkbox-wrapper {
            font-size: clamp(14px, 2.5vw, 18px);

            .ant-checkbox .ant-checkbox-inner {
              width: clamp(16px, 2vw, 20px);
              height: clamp(16px, 2vw, 20px);
              border-color: #0762f7;

              &::after {
                width: clamp(5px, 0.8vw, 7px);
                height: clamp(9px, 1.3vw, 12px);
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(0);
                transition: all 0.2s ease-in-out;
              }
            }

            .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
              background-color: #0762f7;
              border-color: #0762f7;

              &::after {
                transform: rotate(45deg) scale(1);
              }
            }
          }
        }

        .helper-text {
          font-size: clamp(10px, 1.8vw, 14px);
          margin-left: clamp(5px, 1vw, 10px);
          margin-top: clamp(5px, 1vw, 10px);
        }

        .btn-save {
          min-width: clamp(180px, 25vw, 250px);
          height: clamp(45px, 6vw, 60px);
          font-size: clamp(14px, 2.5vw, 18px);

          svg {
            width: clamp(18px, 2vw, 24px);
            height: clamp(18px, 2vw, 24px);
          }
        }
      }
    }

    .content {
      .card-content {
        .success-content {
          gap: clamp(3px, 0.8vw, 8px);

          .stt-content {
            margin: clamp(5px, 0.8vw, 10px) auto;
            padding: clamp(8px, 1.5vw, 15px);

            .number {
              font-size: clamp(24px, 6vw, 48px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(10px, 2vw, 14px);
            }
          }
        }

        .footer {
          margin-top: clamp(5px, 0.8vw, 10px);

          .footer-text {
            font-size: clamp(10px, 2vw, 14px);
            margin-bottom: clamp(3px, 0.5vw, 6px);
          }

          .image img {
            height: clamp(18px, 2vw, 28px);
            width: clamp(18px, 2vw, 28px);
          }
        }
      }
    }
  }

  @media (max-width: ${breakpoints.tablet}) {
    .content {
      width: 95vw;
      margin-bottom: 10px;

      .card-content {
        padding: 20px 15px;

        .input-wrapper .input-search {
          min-width: 250px;
          padding: 15px 20px;
        }
      }
    }
  }

  @media (min-width: ${breakpoints.kiosk}) {
    .content {
      max-width: 1000px;

      .card-content {
        padding: 50px;

        .icon-wrapper {
          svg {
            height: clamp(120px, 15vh, 200px);
          }
        }

        .input-wrapper .input-search {
          max-width: 700px;
          padding: 25px 30px;
        }

        .success-content .stt-content {
          width: 600px;
          padding: 50px 40px;
        }
      }
    }
  }

  @media (max-width: 800px) {
    .content {
      &.no-header {
        margin-top: 5px;
      }
    }
  }

  /* HD 768x1366 portrait - font-size gần giống iPad Pro nhưng giảm theo tỷ lệ */
  @media (max-width: 768px) and (min-height: 1300px) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(24px, 4.5vh, 60px) !important;
      }
      .sub-header {
        font-size: clamp(18px, 3.5vh, 45px) !important;
      }
    }
    .content {
      width: 95vw;
      max-width: 720px;
      max-height: 92vh;
      margin: 0 auto;
      border-radius: 16px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(8px, 1.5vh, 15px) !important;
        max-height: 96vh;
      }

      .header-content {
        font-size: clamp(22px, 4vh, 50px) !important;
      }

      .card-content {
        padding: clamp(12px, 2.5vh, 25px) clamp(15px, 2.5vw, 25px) !important;

        h1 {
          font-size: clamp(22px, 4.5vh, 55px) !important;
          margin-bottom: clamp(8px, 1.5vh, 15px) !important;
        }

        p {
          font-size: clamp(16px, 3.2vh, 40px) !important;
          margin-bottom: clamp(10px, 2vh, 20px) !important;
        }

        .icon-wrapper {
          justify-content: space-around;
          svg {
            height: clamp(80px, 12vh, 160px) !important;
            width: auto;
          }
        }

        .input-wrapper {
          .input-search-container {
            max-width: 90vw !important;
          }
          .input-search {
            min-width: 300px !important;
            max-width: 85vw !important;
            padding: clamp(12px, 2.5vh, 20px) clamp(18px, 3vw, 25px) !important;
            font-size: clamp(15px, 2vh, 28px) !important;
            margin-top: clamp(12px, 2.5vh, 18px);

            .ant-input-affix-wrapper {
              font-size: clamp(15px, 2vh, 28px) !important;
              input {
                font-size: clamp(15px, 2vh, 28px) !important;
              }
            }
            .ant-input-suffix {
              svg {
                width: clamp(18px, 2.2vh, 32px);
                height: clamp(18px, 2.2vh, 32px);
                color: ${btnGreen};
              }
            }
          }
        }

        .success-content {
          gap: clamp(10px, 2vh, 18px);

          svg {
            width: clamp(60px, 8vh, 120px) !important;
            height: clamp(60px, 8vh, 120px) !important;
          }

          h1 {
            font-size: clamp(22px, 4.5vh, 60px);
          }

          p {
            font-size: clamp(16px, 3vh, 40px);
          }

          .stt-content {
            width: 90%;
            max-width: 650px;
            margin: clamp(8px, 1.5vh, 15px) auto;
            padding: clamp(18px, 3vh, 30px);

            .number {
              font-size: clamp(36px, 8vh, 120px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(18px, 3.5vh, 45px);
            }
          }

          .footer {
            margin-top: clamp(8px, 1.5vh, 15px);

            .footer-text {
              font-size: clamp(16px, 2.8vh, 32px);
            }

            .image img {
              height: clamp(18px, 2.8vh, 32px);
              width: clamp(18px, 2.8vh, 32px);
            }
          }
        }
        .error-content {
          svg {
            width: clamp(60px, 8vh, 120px) !important;
            height: clamp(60px, 8vh, 120px) !important;
          }
          h1 {
            font-size: clamp(22px, 4.5vh, 60px);
          }
        }
      }
    }

    /* Form styles for HD 768x1366 */
    .form-header {
      margin-bottom: clamp(18px, 3vh, 28px);
      padding-bottom: clamp(15px, 2.5vh, 22px);

      svg {
        width: clamp(28px, 4vh, 40px);
        height: clamp(28px, 4vh, 40px);
      }

      span {
        font-size: clamp(20px, 3.5vh, 32px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(18px, 3vh, 28px) !important;
          &.ant-form-item-required:not(
              .ant-form-item-required-mark-optional
            )::before {
            font-size: clamp(18px, 3vh, 28px) !important;
            margin-right: clamp(8px, 1.2vh, 15px);
          }
        }

        .ant-form-item-explain-error {
          font-size: clamp(14px, 2.2vh, 20px);
          margin-left: clamp(8px, 1.2vh, 15px);
          text-align: left;
        }

        .ant-input,
        .ant-picker {
          height: clamp(45px, 6vh, 60px);
          font-size: clamp(18px, 3vh, 28px);
          padding: clamp(12px, 2vh, 18px) clamp(18px, 3vw, 25px);
          border-radius: clamp(22px, 3.5vh, 30px);
          border: 3px solid #dce3e9;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          .ant-picker-suffix {
            font-size: clamp(18px, 3vh, 28px);
          }
        }

        /* HD 768x1366 styles cho DOBInput */
        .item-dob {
          .ant-picker {
            height: clamp(50px, 7vh, 70px) !important;
            border-radius: clamp(25px, 4vh, 35px) !important;
            border: 3px solid #0062ff47 !important;

            .ant-picker-input input {
              font-size: clamp(20px, 3.5vh, 32px) !important;
              padding: clamp(12px, 2vh, 18px) clamp(18px, 3vw, 25px) !important;
            }

            .ant-picker-suffix {
              font-size: clamp(20px, 3.5vh, 32px) !important;
              margin-right: clamp(12px, 2vh, 18px);
            }
          }

          .ant-picker-dropdown {
            .ant-picker-panel-container {
              font-size: clamp(16px, 2.8vh, 24px);
              border-radius: 16px;
            }

            .ant-picker-header {
              button {
                font-size: clamp(16px, 2.8vh, 24px);
              }
            }

            .ant-picker-content {
              th,
              td {
                font-size: clamp(14px, 2.5vh, 20px);
                height: clamp(35px, 5vh, 45px);
                line-height: clamp(35px, 5vh, 45px);
              }
            }
          }
        }
        .ant-input-suffix {
          span {
            font-size: clamp(20px, 3.5vh, 32px);
          }
        }

        .ant-radio-group {
          gap: clamp(25px, 4vw, 35px);

          .ant-radio-wrapper {
            font-size: clamp(20px, 3.5vh, 32px);

            .ant-radio .ant-radio-inner {
              width: clamp(20px, 3.5vh, 28px);
              height: clamp(20px, 3.5vh, 28px);

              &::after {
                width: clamp(20px, 3.5vh, 28px);
                height: clamp(20px, 3.5vh, 28px);
              }
            }
          }
        }

        .ant-checkbox-wrapper {
          font-size: clamp(20px, 3.5vh, 32px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(20px, 3.5vh, 28px);
            height: clamp(20px, 3.5vh, 28px);
            border-color: #0762f7;

            &::after {
              width: clamp(6px, 1.2vh, 10px);
              height: clamp(12px, 2vh, 16px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .helper-text {
        font-size: clamp(14px, 2.2vh, 20px);
        margin-left: clamp(8px, 1.2vw, 15px);
        margin-top: clamp(4px, 0.8vh, 8px);
      }

      .btn-save {
        min-width: clamp(220px, 32vw, 360px);
        height: clamp(55px, 7vh, 70px);
        font-size: clamp(18px, 3vh, 28px);
        border-radius: clamp(28px, 4.5vh, 35px);

        svg {
          width: clamp(22px, 3vh, 32px);
          height: clamp(22px, 3vh, 32px);
        }

        span {
          font-size: clamp(20px, 3.5vh, 32px);
        }
      }
    }
  }

  /* iPad 768x1024 portrait - font-size vừa phải */
  @media (max-width: 768px) and (min-height: 1000px) and (max-height: 1299px) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(20px, 2.5vh, 28px);
      }
      .sub-header {
        font-size: clamp(16px, 2vh, 22px);
      }
    }
    .content {
      width: 95vw;
      max-width: 720px;
      max-height: 90vh;
      margin: 0 auto;
      border-radius: 12px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(5px, 1vh, 10px);
        max-height: 95vh;
      }

      .header-content {
        font-size: clamp(18px, 2vh, 24px);
      }

      .card-content {
        padding: clamp(8px, 1.5vh, 15px) clamp(10px, 1.5vw, 18px);

        h1 {
          font-size: clamp(20px, 2.8vh, 28px);
          margin-bottom: clamp(6px, 1vh, 12px);
        }

        p {
          font-size: clamp(15px, 2vh, 20px);
          margin-bottom: clamp(8px, 1.5vh, 15px);
        }

        .input-wrapper {
          .input-search {
            font-size: clamp(14px, 1.8vh, 18px) !important;

            .ant-input-affix-wrapper {
              font-size: clamp(14px, 1.8vh, 18px) !important;
              input {
                font-size: clamp(14px, 1.8vh, 18px) !important;
              }
            }
          }
        }
      }
    }

    /* Form styles for iPad 768x1024 */
    .form-header {
      span {
        font-size: clamp(18px, 2.5vh, 24px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(16px, 2.2vh, 20px) !important;
        }

        .ant-input,
        .ant-picker {
          height: clamp(42px, 5.5vh, 50px);
          font-size: clamp(16px, 2.2vh, 20px);
          border-radius: clamp(21px, 2.8vh, 25px);
        }

        .item-dob {
          .ant-picker {
            height: clamp(42px, 5.5vh, 50px) !important;
            border-radius: clamp(21px, 2.8vh, 25px) !important;

            .ant-picker-input input {
              font-size: clamp(16px, 2.2vh, 20px) !important;
            }
          }
        }

        .ant-radio-wrapper {
          font-size: clamp(16px, 2.2vh, 20px);
        }

        .ant-checkbox-wrapper {
          font-size: clamp(16px, 2.2vh, 20px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(16px, 2.2vh, 20px);
            height: clamp(16px, 2.2vh, 20px);
            border-color: #0762f7;

            &::after {
              width: clamp(5px, 0.8vh, 7px);
              height: clamp(9px, 1.3vh, 12px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .btn-save {
        height: clamp(48px, 6vh, 55px);
        font-size: clamp(16px, 2.2vh, 20px);
        border-radius: clamp(24px, 3vh, 28px);
      }
    }
  }

  /* iPad thường (769px - 1023px) portrait - font-size nhỏ hơn */
  @media (max-width: 1023px) and (min-width: 769px) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(20px, 2.5vh, 28px);
      }
      .sub-header {
        font-size: clamp(16px, 2vh, 22px);
      }
    }
    .content {
      width: 95vw;
      max-width: 900px;
      max-height: 90vh;
      margin: 0 auto;
      border-radius: 12px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(5px, 1vh, 10px);
        max-height: 95vh;
      }

      .header-content {
        font-size: clamp(18px, 2vh, 24px) !important;
      }

      .card-content {
        padding: clamp(8px, 1.5vh, 15px) clamp(10px, 1.5vw, 18px) !important;

        h1 {
          font-size: clamp(22px, 3vh, 32px) !important;
          margin-bottom: clamp(6px, 1vh, 12px) !important;
        }

        p {
          font-size: clamp(16px, 2vh, 22px) !important;
          margin-bottom: clamp(8px, 1.5vh, 15px) !important;
        }

        .icon-wrapper {
          justify-content: space-around;
          svg {
            height: clamp(60px, 8vh, 100px) !important;
            width: auto;
          }
        }

        .input-wrapper {
          .input-search-container {
            max-width: 90vw !important;
          }
          .input-search {
            min-width: 250px !important;
            max-width: 85vw !important;
            padding: clamp(10px, 2vh, 15px) clamp(12px, 2.5vw, 18px) !important;
            font-size: clamp(14px, 2vh, 20px) !important;
            margin-top: clamp(10px, 2vh, 15px);

            .ant-input-affix-wrapper {
              font-size: clamp(14px, 2vh, 20px) !important;
              input {
                font-size: clamp(14px, 2vh, 20px) !important;
              }
            }
            .ant-input-suffix {
              svg {
                width: clamp(16px, 2vh, 24px);
                height: clamp(16px, 2vh, 24px);
                color: ${btnGreen};
              }
            }
          }
        }

        .success-content {
          gap: clamp(8px, 1.5vh, 15px);

          svg {
            width: clamp(40px, 6vh, 80px) !important;
            height: clamp(40px, 6vh, 80px) !important;
          }

          h1 {
            font-size: clamp(20px, 2.5vh, 28px);
          }

          p {
            font-size: clamp(14px, 1.8vh, 20px);
          }

          .stt-content {
            width: 90%;
            max-width: 600px;
            margin: clamp(6px, 1vh, 12px) auto;
            padding: clamp(12px, 2vh, 20px);

            .number {
              font-size: clamp(32px, 6vh, 60px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(16px, 2.5vh, 24px);
            }
          }

          .footer {
            margin-top: clamp(6px, 1vh, 12px);

            .footer-text {
              font-size: clamp(14px, 2vh, 20px);
            }

            .image img {
              height: clamp(16px, 2vh, 32px);
              width: clamp(16px, 2vh, 32px);
            }
          }
        }
        .error-content {
          svg {
            width: clamp(40px, 6vh, 80px) !important;
            height: clamp(40px, 6vh, 80px) !important;
          }
          h1 {
            font-size: clamp(20px, 2.5vh, 28px);
          }
        }
      }
    }

    /* Form styles for iPad thường */
    .form-header {
      margin-bottom: clamp(15px, 2vh, 25px);
      padding-bottom: clamp(12px, 2vh, 20px);

      svg {
        width: clamp(24px, 3vh, 32px);
        height: clamp(24px, 3vh, 32px);
      }

      span {
        font-size: clamp(18px, 2.5vh, 24px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(14px, 2vh, 18px) !important;
          &.ant-form-item-required:not(
              .ant-form-item-required-mark-optional
            )::before {
            font-size: clamp(14px, 2vh, 18px) !important;
            margin-right: clamp(6px, 0.8vh, 10px);
          }
        }

        .ant-form-item-explain-error {
          font-size: clamp(12px, 1.5vh, 16px);
          margin-left: clamp(6px, 0.8vh, 10px);
          text-align: left;
        }

        .ant-input,
        .ant-picker {
          height: clamp(40px, 5vh, 50px);
          font-size: clamp(14px, 2vh, 18px);
          padding: clamp(10px, 1.5vh, 14px) clamp(12px, 2vw, 16px);
          border-radius: clamp(20px, 2.5vh, 25px);
          border: 3px solid #dce3e9;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          .ant-picker-suffix {
            font-size: clamp(14px, 2vh, 18px);
          }
        }

        /* iPad thường styles cho DOBInput */
        .item-dob {
          .ant-picker {
            height: clamp(40px, 5vh, 50px) !important;
            border-radius: clamp(20px, 2.5vh, 25px) !important;
            border: 3px solid #0062ff47 !important;

            .ant-picker-input input {
              font-size: clamp(14px, 2vh, 18px) !important;
              padding: clamp(10px, 1.5vh, 14px) clamp(12px, 2vw, 16px) !important;
            }

            .ant-picker-suffix {
              font-size: clamp(14px, 2vh, 18px) !important;
              margin-right: clamp(10px, 1.5vh, 14px);
            }
          }

          .ant-picker-dropdown {
            .ant-picker-panel-container {
              font-size: clamp(12px, 1.8vh, 16px);
              border-radius: 16px;
            }

            .ant-picker-header {
              button {
                font-size: clamp(12px, 1.8vh, 16px);
              }
            }

            .ant-picker-content {
              th,
              td {
                font-size: clamp(10px, 1.5vh, 14px);
                height: clamp(28px, 3.5vh, 36px);
                line-height: clamp(28px, 3.5vh, 36px);
              }
            }
          }
        }
        .ant-input-suffix {
          span {
            font-size: clamp(14px, 2vh, 18px);
          }
        }

        .ant-radio-group {
          gap: clamp(20px, 3vw, 30px);

          .ant-radio-wrapper {
            font-size: clamp(14px, 2vh, 18px);

            .ant-radio .ant-radio-inner {
              width: clamp(16px, 2vh, 20px);
              height: clamp(16px, 2vh, 20px);

              &::after {
                width: clamp(16px, 2vh, 20px);
                height: clamp(16px, 2vh, 20px);
              }
            }
          }
        }

        .ant-checkbox-wrapper {
          font-size: clamp(14px, 2vh, 18px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(16px, 2vh, 20px);
            height: clamp(16px, 2vh, 20px);
            border-color: #0762f7;

            &::after {
              width: clamp(5px, 0.8vh, 7px);
              height: clamp(9px, 1.3vh, 12px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .helper-text {
        font-size: clamp(12px, 1.5vh, 16px);
        margin-left: clamp(6px, 0.8vw, 10px);
        margin-top: clamp(3px, 0.6vh, 6px);
      }

      .btn-save {
        min-width: clamp(180px, 25vw, 280px);
        height: clamp(42px, 5vh, 55px);
        font-size: clamp(14px, 2vh, 18px);
        border-radius: clamp(21px, 3vh, 28px);

        svg {
          width: clamp(16px, 2vh, 22px);
          height: clamp(16px, 2vh, 22px);
        }

        span {
          font-size: clamp(14px, 2vh, 18px);
        }
      }
    }
  }

  /* iPad Pro và màn hình lớn (1080px+) portrait - GIỮ NGUYÊN FONT-SIZE LỚN */
  @media (min-width: 769px) and (max-width: ${breakpoints.kioskPortrait}) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(24px, 3vh, 42px);
      }
      .sub-header {
        font-size: clamp(18px, 2.5vh, 38px);
      }
    }
    .content {
      width: 95vw;
      max-width: 1000px;
      max-height: 90vh;
      margin: 0 auto;
      border-radius: 12px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(5px, 1vh, 10px);
        max-height: 95vh;
      }

      .header-content {
        font-size: clamp(20px, 2.5vh, 36px);
      }

      .card-content {
        padding: clamp(10px, 2vh, 20px) clamp(12px, 2vw, 20px);

        h1 {
          font-size: clamp(28px, 6vh, 80px);
          margin-bottom: clamp(8px, 1.5vh, 15px);
        }

        p {
          font-size: clamp(20px, 4.5vh, 60px);
          margin-bottom: clamp(10px, 2vh, 20px);
        }

        .icon-wrapper {
          justify-content: space-around;
          svg {
            height: auto;
            width: auto;
          }
        }

        .input-wrapper {
          .input-search-container {
            max-width: 90vw !important;
          }
          .input-search {
            min-width: 280px !important;
            max-width: 90vw !important;
            padding: clamp(12px, 2.5vh, 18px) clamp(15px, 3vw, 20px) !important;
            font-size: clamp(18px, 2.5vh, 38px) !important;
            margin-top: clamp(12px, 2.5vh, 18px);

            .ant-input-affix-wrapper {
              font-size: clamp(18px, 2.5vh, 38px) !important;
              input {
                font-size: clamp(18px, 2.5vh, 38px) !important;
              }
            }
            .ant-input-suffix {
              svg {
                width: clamp(20px, 2.5vh, 38px);
                height: clamp(20px, 2.5vh, 38px);
                color: ${btnGreen};
              }
            }
          }
        }
        .loading-text {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }
        .item-dob input,
        .address-full-wrapper input {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }
        .address-full-wrapper {
          ul {
            border-radius: 12px;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.18),
              0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: 320px;
            padding: 6px 0;

            li {
              font-size: clamp(18px, 2.5vh, 38px) !important;
              padding: 14px 18px;
              margin: 2px 6px;

              &:hover {
                transform: translateX(6px);
                box-shadow: 0 3px 12px rgba(7, 98, 247, 0.2);
              }
            }
          }
        }

        .btn-action {
          font-size: clamp(18px, 2.5vh, 38px) !important;
        }

        .success-content {
          gap: clamp(8px, 1.5vh, 15px);

          svg {
            width: clamp(50px, 10vh, 120px) !important;
            height: clamp(50px, 10vh, 120px) !important;
          }

          h1 {
            font-size: clamp(28px, 6vh, 80px);
          }

          p {
            font-size: clamp(20px, 4.5vh, 60px);
          }

          .stt-content {
            width: 90%;
            max-width: 800px;
            margin: clamp(8px, 1.5vh, 15px) auto;
            padding: clamp(15px, 3vh, 25px);

            .number {
              font-size: clamp(40px, 10vh, 180px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(20px, 3.5vh, 44px);
            }
          }

          .footer {
            margin-top: clamp(8px, 1.5vh, 15px);

            .footer-text {
              font-size: clamp(18px, 3vh, 38px);
            }

            .image img {
              height: clamp(20px, 3vh, 60px);
              width: clamp(20px, 3vh, 60px);
            }
          }
        }
        .error-content {
          svg {
            width: clamp(60px, 8vh, 120px) !important;
            height: clamp(60px, 8vh, 120px) !important;
          }
          h1 {
            font-size: clamp(28px, 6vh, 80px);
          }
        }
      }
    }

    /* Form styles for kiosk portrait */
    .form-header {
      margin-bottom: clamp(20px, 3vh, 30px);
      padding-bottom: clamp(15px, 2.5vh, 25px);

      svg {
        width: clamp(32px, 5vh, 45px);
        height: clamp(32px, 5vh, 45px);
      }

      span {
        font-size: clamp(28px, 5vh, 42px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(24px, 4vh, 36px) !important;
          &.ant-form-item-required:not(
              .ant-form-item-required-mark-optional
            )::before {
            font-size: clamp(24px, 4vh, 36px) !important;
            margin-right: clamp(10px, 1.5vh, 20px);
          }
        }

        .ant-form-item-explain-error {
          font-size: clamp(18px, 3vh, 28px);
          margin-left: clamp(10px, 1.5vh, 20px);
          text-align: left;
        }

        .ant-input,
        .ant-picker {
          height: clamp(60px, 8vh, 80px);
          font-size: clamp(24px, 4vh, 36px);
          padding: clamp(15px, 2.5vh, 22px) clamp(20px, 3.5vw, 30px);
          border-radius: clamp(30px, 5vh, 40px);
          border: 3px solid #dce3e9;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          .ant-picker-suffix {
            font-size: clamp(24px, 4vh, 36px);
          }
        }

        /* Kiosk portrait styles cho DOBInput */
        .item-dob {
          .ant-picker {
            height: clamp(45px, 6vh, 60px) !important;
            border-radius: clamp(22px, 3.5vh, 30px) !important;
            border: 2px solid #0062ff47 !important;

            .ant-picker-input input {
              font-size: clamp(18px, 3vh, 28px) !important;
              padding: clamp(12px, 2vh, 18px) clamp(16px, 2.5vw, 24px) !important;
            }

            .ant-picker-suffix {
              font-size: clamp(18px, 3vh, 28px) !important;
              margin-right: clamp(12px, 2vh, 18px);
            }
          }

          .ant-picker-dropdown {
            .ant-picker-panel-container {
              font-size: clamp(16px, 2.5vh, 24px);
              border-radius: 12px;
            }

            .ant-picker-header {
              button {
                font-size: clamp(16px, 2.5vh, 24px);
              }
            }

            .ant-picker-content {
              th,
              td {
                font-size: clamp(14px, 2.2vh, 20px);
                height: clamp(32px, 4.5vh, 40px);
                line-height: clamp(32px, 4.5vh, 40px);
              }
            }
          }
        }
        .ant-input-suffix {
          span {
            font-size: clamp(18px, 3vh, 28px);
          }
        }

        .ant-radio-group {
          gap: clamp(20px, 3.5vw, 30px);

          .ant-radio-wrapper {
            font-size: clamp(18px, 3vh, 28px);

            .ant-radio .ant-radio-inner {
              width: clamp(18px, 3vh, 24px);
              height: clamp(18px, 3vh, 24px);

              &::after {
                width: clamp(18px, 3vh, 24px);
                height: clamp(18px, 3vh, 24px);
              }
            }
          }
        }

        .ant-checkbox-wrapper {
          font-size: clamp(18px, 3vh, 28px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(18px, 3vh, 24px);
            height: clamp(18px, 3vh, 24px);
            border-color: #0762f7;

            &::after {
              width: clamp(6px, 1vh, 9px);
              height: clamp(10px, 1.8vh, 14px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .helper-text {
        font-size: clamp(14px, 2.2vh, 20px);
        margin-left: clamp(8px, 1.2vw, 16px);
        margin-top: clamp(4px, 0.8vh, 8px);
      }

      .btn-save {
        min-width: clamp(240px, 35vw, 380px);
        height: clamp(50px, 7vh, 70px);
        font-size: clamp(18px, 3vh, 28px);
        border-radius: clamp(25px, 4vh, 35px);

        svg {
          width: clamp(20px, 3vh, 28px);
          height: clamp(20px, 3vh, 28px);
        }

        span {
          font-size: clamp(24px, 4vh, 36px);
        }
      }
    }
  }

  /* iPad Pro 1024x1366 portrait - FONT-SIZE NHỎ HƠN - ĐỘ ƯU TIÊN CAO NHẤT */
  @media (min-width: 1024px) and (max-width: 1024px) and (min-height: 1300px) and (orientation: portrait) {
    .top .header {
      .title {
        font-size: clamp(20px, 2.5vh, 32px);
      }
      .sub-header {
        font-size: clamp(16px, 2vh, 28px);
      }
    }
    .content {
      width: 95vw;
      max-width: 1000px;
      max-height: 90vh;
      margin: 0 auto;
      border-radius: 12px;
      overflow-y: auto;

      &.no-header {
        margin-top: clamp(5px, 1vh, 10px);
        max-height: 95vh;
      }

      .header-content {
        font-size: clamp(18px, 2vh, 28px);
      }

      .card-content {
        padding: clamp(10px, 2vh, 20px) clamp(12px, 2vw, 20px);

        h1 {
          font-size: clamp(24px, 4vh, 50px);
          margin-bottom: clamp(8px, 1.5vh, 15px);
        }

        p {
          font-size: clamp(18px, 3vh, 40px);
          margin-bottom: clamp(10px, 2vh, 20px);
        }

        .icon-wrapper {
          justify-content: space-around;
          svg {
            height: auto;
            width: auto;
          }
        }

        .input-wrapper {
          .input-search-container {
            max-width: 90vw !important;
          }
          .input-search {
            min-width: 280px !important;
            max-width: 90vw !important;
            padding: clamp(10px, 2vh, 16px) clamp(15px, 3vw, 20px) !important;
            font-size: clamp(16px, 2vh, 28px) !important;
            margin-top: clamp(10px, 2vh, 16px);

            .ant-input-affix-wrapper {
              font-size: clamp(16px, 2vh, 28px) !important;
              input {
                font-size: clamp(16px, 2vh, 28px) !important;
              }
            }
            .ant-input-suffix {
              svg {
                width: clamp(18px, 2vh, 28px);
                height: clamp(18px, 2vh, 28px);
                color: ${btnGreen};
              }
            }
          }
        }
        .loading-text {
          font-size: clamp(16px, 2vh, 28px) !important;
        }
        .item-dob input,
        .address-full-wrapper input {
          font-size: clamp(16px, 2vh, 28px) !important;
        }
        .address-full-wrapper {
          ul {
            border-radius: 12px;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.18),
              0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: 320px;
            padding: 6px 0;

            li {
              font-size: clamp(16px, 2vh, 28px) !important;
              padding: 14px 18px;
              margin: 2px 6px;

              &:hover {
                transform: translateX(6px);
                box-shadow: 0 3px 12px rgba(7, 98, 247, 0.2);
              }
            }
          }
        }

        .btn-action {
          font-size: clamp(16px, 2vh, 28px) !important;
        }

        .success-content {
          gap: clamp(8px, 1.5vh, 15px);

          svg {
            width: clamp(40px, 6vh, 80px) !important;
            height: clamp(40px, 6vh, 80px) !important;
          }

          h1 {
            font-size: clamp(24px, 4vh, 50px);
          }

          p {
            font-size: clamp(18px, 3vh, 40px);
          }

          .stt-content {
            width: 90%;
            max-width: 800px;
            margin: clamp(8px, 1.5vh, 15px) auto;
            padding: clamp(15px, 3vh, 25px);

            .number {
              font-size: clamp(32px, 6vh, 100px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(18px, 2.5vh, 32px);
            }
          }

          .footer {
            margin-top: clamp(8px, 1.5vh, 15px);

            .footer-text {
              font-size: clamp(16px, 2.5vh, 28px);
            }

            .image img {
              height: clamp(18px, 2.5vh, 40px);
              width: clamp(18px, 2.5vh, 40px);
            }
          }
        }
        .error-content {
          svg {
            width: clamp(40px, 6vh, 80px) !important;
            height: clamp(40px, 6vh, 80px) !important;
          }
          h1 {
            font-size: clamp(24px, 4vh, 50px);
          }
        }
      }
    }

    /* Form styles for 1024x1366 portrait */
    .form-header {
      margin-bottom: clamp(20px, 3vh, 30px);
      padding-bottom: clamp(15px, 2.5vh, 25px);

      svg {
        width: clamp(28px, 4vh, 36px);
        height: clamp(28px, 4vh, 36px);
      }

      span {
        font-size: clamp(24px, 4vh, 32px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(20px, 3vh, 28px) !important;
          &.ant-form-item-required:not(
              .ant-form-item-required-mark-optional
            )::before {
            font-size: clamp(20px, 3vh, 28px) !important;
            margin-right: clamp(8px, 1.2vh, 16px);
          }
        }

        .ant-form-item-explain-error {
          font-size: clamp(14px, 2vh, 22px);
          margin-left: clamp(8px, 1.2vh, 16px);
          text-align: left;
        }

        .ant-input,
        .ant-picker {
          height: clamp(45px, 6vh, 60px);
          font-size: clamp(18px, 3vh, 28px);
          padding: clamp(12px, 2vh, 18px) clamp(16px, 2.5vw, 24px);
          border-radius: clamp(22px, 3.5vh, 30px);
          border: 2px solid #dce3e9;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
          .ant-picker-suffix {
            font-size: clamp(18px, 3vh, 28px);
          }
        }

        /* Kiosk portrait styles cho DOBInput */
        .item-dob {
          .ant-picker {
            height: clamp(45px, 6vh, 60px) !important;
            border-radius: clamp(22px, 3.5vh, 30px) !important;
            border: 2px solid #0062ff47 !important;

            .ant-picker-input input {
              font-size: clamp(18px, 3vh, 28px) !important;
              padding: clamp(12px, 2vh, 18px) clamp(16px, 2.5vw, 24px) !important;
            }

            .ant-picker-suffix {
              font-size: clamp(18px, 3vh, 28px) !important;
              margin-right: clamp(12px, 2vh, 18px);
            }
          }

          .ant-picker-dropdown {
            .ant-picker-panel-container {
              font-size: clamp(16px, 2.5vh, 24px);
              border-radius: 12px;
            }

            .ant-picker-header {
              button {
                font-size: clamp(16px, 2.5vh, 24px);
              }
            }

            .ant-picker-content {
              th,
              td {
                font-size: clamp(14px, 2.2vh, 20px);
                height: clamp(32px, 4.5vh, 40px);
                line-height: clamp(32px, 4.5vh, 40px);
              }
            }
          }
        }
        .ant-input-suffix {
          span {
            font-size: clamp(18px, 3vh, 28px);
          }
        }

        .ant-radio-group {
          gap: clamp(20px, 3.5vw, 30px);

          .ant-radio-wrapper {
            font-size: clamp(18px, 3vh, 28px);

            .ant-radio .ant-radio-inner {
              width: clamp(18px, 3vh, 24px);
              height: clamp(18px, 3vh, 24px);

              &::after {
                width: clamp(18px, 3vh, 24px);
                height: clamp(18px, 3vh, 24px);
              }
            }
          }
        }

        .ant-checkbox-wrapper {
          font-size: clamp(18px, 3vh, 28px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(18px, 3vh, 24px);
            height: clamp(18px, 3vh, 24px);
            border-color: #0762f7;

            &::after {
              width: clamp(6px, 1vh, 9px);
              height: clamp(10px, 1.8vh, 14px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .helper-text {
        font-size: clamp(14px, 2.2vh, 20px);
        margin-left: clamp(8px, 1.2vw, 16px);
        margin-top: clamp(4px, 0.8vh, 8px);
      }

      .btn-save {
        min-width: clamp(240px, 35vw, 380px);
        height: clamp(50px, 7vh, 70px);
        font-size: clamp(18px, 3vh, 28px);
        border-radius: clamp(25px, 4vh, 35px);

        svg {
          width: clamp(20px, 3vh, 28px);
          height: clamp(20px, 3vh, 28px);
        }
      }
    }
  }

  /* iPad 768x1024 landscape */
  @media (max-width: 1024px) and (max-height: 768px) and (orientation: landscape) {
    .content {
      width: 90vw;
      max-width: 900px;
      margin: 0 auto;

      .card-content {
        padding: clamp(12px, 2vh, 18px) clamp(12px, 1.5vw, 18px);

        h1 {
          font-size: clamp(18px, 2.5vh, 24px);
          margin-bottom: clamp(10px, 1.5vh, 15px);
        }

        p {
          font-size: clamp(13px, 1.8vh, 16px);
          margin-bottom: clamp(12px, 2vh, 16px);
        }

        .input-wrapper .input-search {
          font-size: clamp(13px, 1.8vh, 16px);
        }
      }
    }

    /* Form styles for iPad 768x1024 landscape */
    .form-header {
      span {
        font-size: clamp(15px, 2vh, 18px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(12px, 1.6vh, 15px) !important;
        }

        .ant-input,
        .ant-picker {
          height: clamp(32px, 3.5vh, 38px);
          font-size: clamp(12px, 1.6vh, 15px);
          border-radius: clamp(16px, 1.8vh, 19px);
        }

        .item-dob {
          .ant-picker {
            height: clamp(32px, 3.5vh, 38px) !important;
            border-radius: clamp(16px, 1.8vh, 19px) !important;

            .ant-picker-input input {
              font-size: clamp(12px, 1.6vh, 15px) !important;
            }
          }
        }

        .ant-radio-wrapper {
          font-size: clamp(12px, 1.6vh, 15px);
        }

        .ant-checkbox-wrapper {
          font-size: clamp(12px, 1.6vh, 15px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(12px, 1.6vh, 15px);
            height: clamp(12px, 1.6vh, 15px);
            border-color: #0762f7;

            &::after {
              width: clamp(4px, 0.6vh, 5px);
              height: clamp(7px, 1vh, 9px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .btn-save {
        height: clamp(35px, 4vh, 42px);
        font-size: clamp(12px, 1.6vh, 15px);
        border-radius: clamp(18px, 2vh, 21px);
      }
    }
  }

  /* iPad thường landscape (769px - 1024px ngang) */
  @media (max-width: 1024px) and (min-width: 769px) and (orientation: landscape) {
    .content {
      width: 90vw;
      max-width: 900px;
      margin: 0 auto;

      .card-content {
        padding: clamp(15px, 2.5vh, 25px) clamp(15px, 2vw, 25px);

        h1 {
          font-size: clamp(20px, 2.8vh, 28px);
          margin-bottom: clamp(12px, 2vh, 18px);
        }

        p {
          font-size: clamp(14px, 2vh, 18px);
          margin-bottom: clamp(15px, 2.5vh, 20px);
        }

        .input-wrapper .input-search {
          min-width: 300px;
          max-width: 600px;
          padding: clamp(12px, 2vh, 16px) clamp(16px, 2.5vw, 20px);
          font-size: clamp(14px, 2vh, 18px);
        }

        .success-content {
          gap: clamp(8px, 1.5vh, 12px);

          svg {
            width: clamp(35px, 5vh, 50px) !important;
            height: clamp(35px, 5vh, 50px) !important;
          }

          h1 {
            font-size: clamp(18px, 2.5vh, 24px);
          }

          p {
            font-size: clamp(12px, 1.8vh, 16px);
          }

          .stt-content {
            width: clamp(300px, 40vw, 500px);
            margin: clamp(10px, 1.5vh, 15px) auto;
            padding: clamp(15px, 2.5vh, 25px);

            .number {
              font-size: clamp(28px, 6vh, 45px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(10px, 1.5vh, 14px);
            }
          }

          .footer {
            margin-top: clamp(8px, 1.2vh, 12px);

            .footer-text {
              font-size: clamp(10px, 1.5vh, 14px);
            }

            .image img {
              height: clamp(12px, 1.8vh, 18px);
              width: clamp(12px, 1.8vh, 18px);
            }
          }
        }
      }
    }

    /* Form styles for iPad landscape */
    .form-header {
      margin-bottom: clamp(12px, 2vh, 18px);
      padding-bottom: clamp(10px, 1.5vh, 15px);

      svg {
        width: clamp(20px, 2.5vh, 28px);
        height: clamp(20px, 2.5vh, 28px);
      }

      span {
        font-size: clamp(16px, 2.2vh, 20px);
      }
    }

    .form-custom {
      .ant-form-item {
        .ant-form-item-label label {
          font-size: clamp(12px, 1.8vh, 16px) !important;
        }

        .ant-input,
        .ant-picker {
          height: clamp(35px, 4vh, 42px);
          font-size: clamp(12px, 1.8vh, 16px);
          padding: clamp(8px, 1.2vh, 12px) clamp(10px, 1.5vw, 14px);
          border-radius: clamp(18px, 2vh, 22px);
        }

        .item-dob {
          .ant-picker {
            height: clamp(35px, 4vh, 42px) !important;
            border-radius: clamp(18px, 2vh, 22px) !important;

            .ant-picker-input input {
              font-size: clamp(12px, 1.8vh, 16px) !important;
              padding: clamp(8px, 1.2vh, 12px) clamp(10px, 1.5vw, 14px) !important;
            }
          }
        }

        .ant-radio-wrapper {
          font-size: clamp(12px, 1.8vh, 16px);
        }

        .ant-checkbox-wrapper {
          font-size: clamp(12px, 1.8vh, 16px);

          .ant-checkbox .ant-checkbox-inner {
            width: clamp(12px, 1.8vh, 16px);
            height: clamp(12px, 1.8vh, 16px);
            border-color: #0762f7;

            &::after {
              width: clamp(4px, 0.7vh, 6px);
              height: clamp(7px, 1.2vh, 10px);
              border: 2px solid #ffffff;
              border-top: 0;
              border-left: 0;
              transform: rotate(45deg) scale(0);
              transition: all 0.2s ease-in-out;
            }
          }

          .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #0762f7;
            border-color: #0762f7;

            &::after {
              transform: rotate(45deg) scale(1);
            }
          }
        }
      }

      .btn-save {
        min-width: clamp(150px, 20vw, 220px);
        height: clamp(35px, 4vh, 45px);
        font-size: clamp(12px, 1.8vh, 16px);
        border-radius: clamp(18px, 2vh, 22px);

        svg {
          width: clamp(14px, 1.8vh, 18px);
          height: clamp(14px, 1.8vh, 18px);
        }
      }
    }
  }

  /* iPad Pro và màn hình lớn landscape (1080px+) - giữ nguyên font-size lớn */
  @media (min-width: ${breakpoints.kioskPortrait}) and (orientation: landscape) {
    .content {
      width: 85vw;
      max-width: 1200px;
      margin: 0 auto;

      .card-content {
        padding: clamp(25px, 4vh, 40px) clamp(20px, 3vw, 35px);

        h1 {
          font-size: clamp(28px, 4vh, 45px);
          margin-bottom: clamp(18px, 3vh, 30px);
        }

        p {
          font-size: clamp(18px, 2.5vh, 26px);
          margin-bottom: clamp(20px, 4vh, 35px);
        }

        .input-wrapper .input-search {
          min-width: 400px;
          max-width: 800px;
          padding: clamp(18px, 3vh, 25px) clamp(25px, 4vw, 35px);
          font-size: clamp(20px, 3vh, 28px);
        }

        .success-content {
          gap: clamp(12px, 2vh, 20px);

          svg {
            width: clamp(50px, 7vh, 80px) !important;
            height: clamp(50px, 7vh, 80px) !important;
          }

          h1 {
            font-size: clamp(24px, 4vh, 38px);
          }

          p {
            font-size: clamp(16px, 2.5vh, 22px);
          }

          .stt-content {
            width: clamp(400px, 50vw, 700px);
            margin: clamp(15px, 2vh, 25px) auto;
            padding: clamp(25px, 4vh, 40px);

            .number {
              font-size: clamp(40px, 10vh, 70px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(14px, 2.2vh, 18px);
            }
          }

          .footer {
            margin-top: clamp(12px, 2vh, 20px);

            .footer-text {
              font-size: clamp(13px, 2vh, 16px);
            }

            .image img {
              height: clamp(20px, 3vh, 30px);
              width: clamp(20px, 3vh, 30px);
            }
          }
        }
      }
    }
  }

  @media (max-height: 600px) {
    .content {
      .card-content {
        padding: 8px 12px;

        .success-content {
          gap: 5px;

          .stt-content {
            padding: 10px;
            margin: 5px auto;
          }

          .footer {
            margin-top: 5px;
          }
        }
      }
    }
  }

  /* Tablet nhỏ (481px - 767px) */
  @media (max-width: 767px) and (min-width: 481px) {
    .content {
      width: 96vw;
      margin: 5px auto;
      border-radius: 10px;

      .card-content {
        padding: clamp(10px, 2vh, 15px);

        h1 {
          font-size: clamp(18px, 2.5vh, 24px);
          margin-bottom: clamp(8px, 1.2vh, 12px);
        }

        p {
          font-size: clamp(14px, 1.8vh, 18px);
          margin-bottom: clamp(10px, 1.5vh, 15px);
        }

        .input-wrapper .input-search {
          min-width: 220px;
          max-width: 85vw;
          padding: clamp(10px, 1.5vh, 14px) clamp(12px, 2vw, 16px);
          font-size: clamp(14px, 1.8vh, 18px);
        }

        .success-content {
          h1 {
            font-size: clamp(16px, 2.2vh, 22px);
          }

          p {
            font-size: clamp(12px, 1.6vh, 16px);
          }

          .stt-content {
            .number {
              font-size: clamp(24px, 4vh, 36px);
            }

            .stt,
            .sub-txt {
              font-size: clamp(12px, 1.6vh, 16px);
            }
          }
        }
      }
    }

    /* Form styles for tablet nhỏ */
    .form-dang-ky-kham {
      padding: clamp(10px, 1.5vh, 15px);
      max-width: 96vw;
      border-radius: 10px;

      .form-header {
        margin-bottom: clamp(12px, 1.8vh, 18px);
        padding-bottom: clamp(8px, 1.2vh, 12px);

        svg {
          width: clamp(18px, 2.2vh, 24px);
          height: clamp(18px, 2.2vh, 24px);
        }

        span {
          font-size: clamp(16px, 2.2vh, 20px);
        }
      }

      .form-custom {
        .ant-form-item {
          .ant-form-item-label label {
            font-size: clamp(13px, 1.8vh, 16px) !important;
          }

          .ant-form-item-explain-error {
            font-size: clamp(11px, 1.4vh, 14px);
            margin-left: clamp(4px, 0.6vh, 8px);
          }

          .ant-input,
          .ant-picker {
            height: clamp(38px, 4.5vh, 45px);
            font-size: clamp(13px, 1.8vh, 16px);
            padding: clamp(8px, 1.2vh, 12px) clamp(10px, 1.5vw, 14px);
            border-radius: clamp(19px, 2.2vh, 22px);
          }

          .item-dob {
            .ant-picker {
              height: clamp(38px, 4.5vh, 45px) !important;
              border-radius: clamp(19px, 2.2vh, 22px) !important;

              .ant-picker-input input {
                font-size: clamp(13px, 1.8vh, 16px) !important;
                padding: clamp(8px, 1.2vh, 12px) clamp(10px, 1.5vw, 14px) !important;
              }
            }
          }

          .ant-radio-wrapper {
            font-size: clamp(13px, 1.8vh, 16px);
          }

          .ant-checkbox-wrapper {
            font-size: clamp(13px, 1.8vh, 16px);

            .ant-checkbox .ant-checkbox-inner {
              width: clamp(13px, 1.8vh, 16px);
              height: clamp(13px, 1.8vh, 16px);
              border-color: #0762f7;

              &::after {
                width: clamp(4px, 0.7vh, 6px);
                height: clamp(8px, 1.2vh, 10px);
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(0);
                transition: all 0.2s ease-in-out;
              }
            }

            .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
              background-color: #0762f7;
              border-color: #0762f7;

              &::after {
                transform: rotate(45deg) scale(1);
              }
            }
          }
        }

        .btn-save {
          min-width: clamp(160px, 22vw, 250px);
          height: clamp(40px, 4.5vh, 50px);
          font-size: clamp(13px, 1.8vh, 16px);
          border-radius: clamp(20px, 2.2vh, 25px);

          svg {
            width: clamp(15px, 1.8vh, 20px);
            height: clamp(15px, 1.8vh, 20px);
          }
        }
      }
    }
  }

  /* Mobile (dưới 480px) */
  @media (max-width: 480px) {
    .content {
      width: 98vw;
      margin: 2px auto;
      border-radius: 8px;

      .card-content {
        padding: 8px;

        .input-wrapper .input-search {
          min-width: 200px;
          padding: 10px 15px;
        }
      }
    }

    /* Form mobile styles */
    .form-dang-ky-kham {
      padding: 12px;
      max-width: 98vw;
      border-radius: 12px;

      .form-header {
        margin-bottom: 15px;
        padding-bottom: 10px;

        svg {
          width: 20px;
          height: 20px;
        }

        span {
          font-size: 16px;
        }
      }

      .form-custom {
        .ant-form-item {
          .ant-form-item-label label {
            font-size: 14px !important;
          }

          .ant-form-item-explain-error {
            font-size: 11px;
            margin-left: 5px;
            text-align: left;
          }

          .ant-input,
          .ant-picker {
            height: 40px;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 20px;
          }

          /* Mobile styles cho DOBInput */
          .item-dob {
            .ant-picker {
              height: 40px !important;
              border-radius: 20px !important;
              border: 2px solid #0062ff47 !important;

              .ant-picker-input input {
                font-size: 14px !important;
                padding: 8px 12px !important;
              }

              .ant-picker-suffix {
                font-size: 14px !important;
                margin-right: 8px;
              }
            }

            .ant-picker-dropdown {
              .ant-picker-panel-container {
                font-size: 12px;
                border-radius: 8px;
              }

              .ant-picker-header {
                button {
                  font-size: 12px;
                }
              }

              .ant-picker-content {
                th,
                td {
                  font-size: 12px;
                  height: 24px;
                  line-height: 24px;
                }
              }
            }
          }
          .ant-input-suffix {
            span {
              font-size: 14px;
            }
          }

          .ant-radio-group {
            gap: 15px;

            .ant-radio-wrapper {
              font-size: 14px;

              .ant-radio .ant-radio-inner {
                width: 16px;
                height: 16px;

                &::after {
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }

          .ant-checkbox-wrapper {
            font-size: 14px;

            .ant-checkbox .ant-checkbox-inner {
              width: 16px;
              height: 16px;
              border-color: #0762f7;

              &::after {
                width: 5px;
                height: 9px;
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(0);
                transition: all 0.2s ease-in-out;
              }
            }

            .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
              background-color: #0762f7;
              border-color: #0762f7;

              &::after {
                transform: rotate(45deg) scale(1);
              }
            }
          }
        }

        .helper-text {
          font-size: 11px;
          margin-left: 5px;
          margin-top: 2px;
        }

        .btn-save {
          min-width: 160px;
          height: 45px;
          font-size: 14px;
          border-radius: 22px;

          svg {
            width: 16px;
            height: 16px;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }
`;
