<!DOCTYPE html>
<html lang="en" class="notranslate browser" translate="no">
  <head>
    <meta charset="utf-8" />
    <meta name="google" content="notranslate" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Content-Language" content="en" />
    <link
      rel="icon"
      id="favicon-svg"
      type="image/svg+xml"
      href="%PUBLIC_URL%/favicon.svg"
    />
    <link rel="icon1" href="%PUBLIC_URL%/favicon.ico" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="pragma" content="no-cache" />
    <!-- <meta
      name="viewport"
      content="width=1500, maximum-scale=5, user-scalable=yes, shrink-to-fit=yes"
    /> -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=yes"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>HIS SAKURA</title>
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="%PUBLIC_URL%/logo192.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="%PUBLIC_URL%/logo192.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="167x167"
      href="%PUBLIC_URL%/logo192.png"
    />
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/logo192.png" />
    <link href="/css/style.css" media="screen, print" rel="stylesheet" />
    <link
      href="/fonts/font.css"
      media="screen, print"
      rel="stylesheet"
      crossorigin="anonymous"
    />
    <link rel="manifest" href="manifest.json" />
    <style>
      #global-loader {
        position: fixed;
        z-index: 9999;
        inset: 0;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .loader-logo img {
        width: 80px;
        margin-bottom: 24px;
      }
      #global-loader .lspinner {
        border: 6px solid #f3f3f3;
        border-top: 6px solid #1976d2;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
        box-sizing: content-box !important;
        font-size: 0 !important;
        background: none !important;
        transition: none !important;
        /* transform: none !important; */
        animation-name: spin !important;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .loader-text {
        color: red;
        font-size: 18px;
        font-weight: bold;
        margin-top: 8px;
        font-family: inherit;
      }
    </style>
  </head>

  <body>
    <div id="global-loader">
      <div class="lspinner"></div>
      <div class="loader-text">Vui lòng chờ</div>
    </div>
    <div id="root"></div>
  </body>
</html>
